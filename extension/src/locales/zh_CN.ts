export const zhCNMessages = {
  // Basic Extension Info
  extensionName: 'Mysta',
  extensionDescription: 'Mysta - 让AI替您操作网页',

  // Navigation and UI
  viewProfile: '查看资料',
  conversations: '对话',
  remoteConversation: '远程对话',
  miniapp: '应用程序',
  newChat: '新建对话',
  allChats: '所有对话',
  user: '用户',
  language: '语言',
  english: 'English',
  chinese: '中文',
  settings: '设置',
  logout: '登出',
  workflowDetail: '任务详情',
  confirm: '确认',
  yes: '是',
  no: '否',
  back: '返回',

  // Actions
  save: '保存',
  cancel: '取消',
  delete: '删除',
  close: '关闭',
  ok: '确定',
  notNow: '稍后',
  getStarted: '开始使用',

  // API Key Management
  setApiKey: '设置API密钥',
  enterApiKey: '输入 $1 API密钥',
  apiKeyDisabled: 'API密钥已禁用',
  goToGetApiKey: '去获取API密钥',
  enableApiKey: '启用您的Mysta API密钥',
  apiKeyRequired: '您需要登录获取API密钥才能使用完整功能',

  // Authentication
  signInWithMysta: '使用Mysta网页版登录',
  mystaAccountDetected: '检测到Mysta账户',

  // Chat Interface
  thinking: '思考中',
  typeMessage: '输入消息...',
  send: '发送',
  attachFile: '附加文件',
  recordAudio: '录制语音',
  workflowMode: '任务模式',
  normalMode: '对话模式',

  // Error Messages
  insufficientCredits: '余额不足。请为您的账户充值。',
  insufficientCreditsTip: '余额不足',
  rechargeTip: '充值',
  errorOccurred: '发生错误。请重试。',

  // TopBanner
  joinTelegramGroup: '🎉 加入我们的电报群获得$5积分！',
  cannotSaveForCurrentSite: '无法为当前网站保存。',
  noMessagesToSave: '没有消息可保存。',
  failedToSaveToSiteMemory: '保存到网站记忆失败。',

  // Success Messages
  savedToSiteMemorySuccessfully: '成功保存到网站记忆！',

  // Tool Execution Status
  executing: '执行中',
  executed: '已执行',
  running: '运行中',
  error: '错误',

  // Home Page
  letAiRunTheWeb: '让AI替您操作网页',
  askAnything: '随心所问\n任意而行',
  startTyping: '您的AI助手已就位',
  privacyDisclaimer: 'Mysta AI助手可能产生不准确的信息。您的数据保持私密。',

  // Conversation Management
  deleteConversation: '删除对话',
  deleteConversationConfirm: '您确定要删除此对话吗？此操作无法撤销。',
  newChatItem: '新对话',
  renameConversation: '重命名对话',
  enterNewName: '输入新名称',

  // Site Memory
  saveToSiteMemory: '保存到网站记忆',
  saveToSiteMemoryTitle: '保存到网站记忆',

  // Tooltips
  tooltipNewChat: '新建对话',
  tooltipClose: '关闭',
  tooltipDelete: '删除',
  tooltipConversations: '对话列表',
  tooltipUser: '用户',
  tooltipSaveToSiteMemory: '保存到网站记忆',
  tooltipRename: '重命名',

  // Prompt Templates
  summarizeElonTweet: '总结埃隆·马斯克的最新推文。',
  postTweet: "发布一条推文：'mysta太棒了'。",
  searchLinkedIn: '在我的LinkedIn上搜索MystaAI。',

  // Copy Functionality
  copyToClipboard: '复制到剪贴板',
  copied: '已复制！',

  // File Upload
  selectFile: '选择文件',
  uploadFile: '上传文件',

  // Status
  idle: '空闲',
  waiting: '等待中',
  processing: '处理中',

  // Common Actions
  edit: '编辑',
  share: '分享',
  export: '导出',
  import: '导入',
  refresh: '刷新',
  retry: '重试',

  // Validation Messages
  fieldRequired: '此字段为必填项',
  invalidEmail: '请输入有效的邮箱地址',
  invalidUrl: '请输入有效的URL',

  // Loading States
  loading: '加载中...',
  pleaseWait: '请稍候...',

  // Search
  search: '搜索',
  searchPlaceholder: '搜索对话...',
  noResults: '未找到结果',

  // Time/Date
  now: '现在',
  today: '今天',
  yesterday: '昨天',
  thisWeek: '本周',
  lastWeek: '上周',

  // Permissions
  permissionDenied: '权限被拒绝',
  accessRestricted: '访问受限',

  // Connection
  connectionError: '连接错误',
  networkError: '网络错误',
  retryConnection: '重新连接',

  // Features
  comingSoon: '即将推出',
  betaFeature: '测试功能',
  experimental: '实验性功能',

  // Reasoning
  thoughtProcess: '思考过程',

  // Model Settings
  setApiKeyTooltip: '设置API密钥',
  modelName: 'Mysta',

  // Telegram Configuration
  configureTelegramBot: '配置Telegram机器人',
  botToken: '机器人令牌',
  groupChatId: '群组聊天ID',
  botTokenRequired: '机器人令牌为必填项',
  invalidBotTokenFormat: '无效的机器人令牌格式',
  groupChatIdRequired: '群组聊天ID为必填项',
  invalidGroupChatIdFormat: '无效的群组聊天ID格式',
  getBotTokenFromBotFather: '从Telegram的@BotFather获取',
  getChatIdFromUserInfoBot: '将@userinfobot添加到您的群组以获取聊天ID',
  createRemoteConversation: '创建远程对话',
  creating: '创建中...',

  // Remote Conversation
  remote: '远程模式',
  active: '运行中',
  inactive: '加载中',
  exitRemoteMode: '退出远程模式',
  exitRemoteModeConfirm: '您确定要退出远程模式吗? 请再次确认。',
  logoutTelegram: '登出Telegram',
  logoutTelegramConfirm: '您确定要登出Telegram吗? 请再次确认。',
  switchBot: '切换Bot账号',
  login: '登录',
  loginTip1: '在手机上打开Telegram',
  loginTip2: '进入设置 > 设备 > 链接桌面设备',
  loginTip3: '将手机对准此屏幕以确认登录',
  bindBotManually: '手动绑定Bot',
  enterPassword: '输入密码',
  passwordTip1: '您已启用两步验证，因此您的账户受到额外密码的保护。',
  hint: '提示',
  next: '下一步',
  bindTelegramBot: '绑定Telegram Bot',
  getBotFromBotFather: '从Telegram的@BotFather获取',
  botFatherChatNotFound: '未找到BotFather聊天',
  botListNotFound: '未找到Bot列表',
  botFatherReplyButtonNotFound: '未找到BotFather回复按钮',
  apiTokenButtonNotFound: '未找到API Token按钮',
  bindGroupChat: '绑定群组聊天',
  startBot: '启动Bot',
  startBotWithGroup: '绑定群组后启动',
  addBotToGroup: '首先将$1添加到您的群组并设置为管理员',
  botIsNotAnAdminOfTheGroup: 'Bot不是群组管理员',
  botIsNotAMemberOfTheGroup: 'Bot不是群组成员',
  noMessagesReceivedYet: '暂无消息。发送消息到@$1查看。',
  cannotStartBotWithTime: '无法启动Bot。您需要等待$1',
  cannotStartBot: '无法启动Bot，请稍后重试',
  retryWithTime: '$1后重试',

  // MiniApps
  archived: '已归档',
  installed: '已安装',
  newProject: '新建项目',
  create: '创建',
  createProjectTitle: '创建项目',
  projectNameLabel: '项目名称',
  pleaseEnterPlaceholder: '请输入...',
  deleteMiniappTitle: '删除 MiniAPP',
  deleteMiniappContent: '这将永久删除该 MiniAPP 及其对话。此操作无法撤销。',
  filterAll: '全部',
  filterDeployed: '已安装',
  filterDeveloping: '未安装',
  miniappStatusDeployed: '已部署',
  miniappStatusDeveloping: '开发中',
  archive: '归档',
  activate: '激活',
  archivingTitle: '归档',
  archiveConfirm1: '您即将归档该 MiniAPP。',
  archiveConfirm2: '归档后，需要激活',
  archiveConfirm3: '才能继续使用。',
  emptyMiniapps: '您当前还没有任何 MiniAPP。',
  emptyArchivedMiniapps: '您当前没有任何已归档的 MiniAPP。',

  // MiniApp Detail
  miniappDetailHeaderDevelop: '开发',
  miniappDetailHeaderHistory: '历史版本',
  miniappDetailHello: '您好，',
  miniappDetailImAssistant: '我是 MiniAPP 助手',
  miniappDetailTellMe: '告诉我你想创建什么脚本',
  miniappDetailNotFound: '未找到 MiniAPP',
  miniappDetailVersion: '版本 $1',
  miniappDetailInstalledOn: '安装于 $1',
  miniappDetailGeneratedOn: '生成于 $1',
  miniappDetailViewAllVersions: '查看所有版本',
} as const;
