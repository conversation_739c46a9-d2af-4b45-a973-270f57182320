import { useMemo, useState } from 'react';
import { MiniApp } from '@the-agent/shared';
import { Dropdown, Tooltip, Modal, Input } from 'antd';
import { X } from 'lucide-react';

import { useLiveQuery } from 'dexie-react-hooks';
import { db } from '~/storages/indexdb';
import MiniappEmptyState from '~/sidepanel/components/miniapp/MiniappEmptyState';
import MiniappApplicationsList from '~/sidepanel/components/miniapp/MiniappApplicationsList';
import newMiniappImg from '~/assets/imgs/new-miniapp.png';
import { useNavigate } from 'react-router-dom';
import { useUser } from '~/hooks/useUser';
import { createNewMiniApp } from '~/services/conversation';
import {
  deleteMiniapp as deleteMiniappService,
  updateMiniapp as updateMiniappService,
} from '~/services/miniapp';
import { useLanguage } from '~/utils/i18n';
import newminiappIcon from '~/assets/icons/newminiapp.svg';
import archiveIcon from '~/assets/icons/archive.svg';

type FilterType = 'All' | 'Installed' | 'Uninstalled';

export const MiniappsPage = () => {
  const navigate = useNavigate();
  const { getMessage } = useLanguage();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [showArchivedView, setShowArchivedView] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<number | null>(null);
  const activeUser = useUser();
  const [filter, setFilter] = useState<FilterType>('All');

  const miniapps = useLiveQuery(async () => {
    if (!activeUser?.id) return [];
    if (showArchivedView) {
      const archivedApps = await db.getArchivedApps();
      return archivedApps;
    }
    const allApps = await db.getAllApps('all');
    return allApps;
  }, [activeUser?.id, showArchivedView]);

  // Filter miniapps
  const filteredMiniapps = useMemo(() => {
    if (filter === 'All') return miniapps;
    if (filter === 'Installed')
      return miniapps.filter(app => app.installation && app.installation.code);
    if (filter === 'Uninstalled')
      return miniapps.filter(app => !app.installation || !app.installation.code);
    return miniapps;
  }, [miniapps, filter]);

  // Filter dropdown items
  const filterItems = [
    { key: 'All', label: getMessage('filterAll') },
    { key: 'Installed', label: getMessage('filterDeployed') },
    { key: 'Uninstalled', label: getMessage('filterDeveloping') },
  ];

  const filterLabel = useMemo(() => {
    switch (filter) {
      case 'All':
        return getMessage('filterAll');
      case 'Installed':
        return getMessage('filterDeployed');
      case 'Uninstalled':
        return getMessage('filterDeveloping');
      default:
        return filter;
    }
  }, [filter, getMessage]);

  const handleNewProject = () => {
    setShowCreateModal(true);
  };

  const handleCreateProject = async () => {
    if (!projectName.trim() || !activeUser?.id) return;

    try {
      const miniapp = await createNewMiniApp(activeUser.id, projectName.trim());
      navigate(`/miniapp/${miniapp.id}`);

      setShowCreateModal(false);
      setProjectName('');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleCancelCreate = () => {
    setShowCreateModal(false);
    setProjectName('');
  };

  const handleSelectMiniapp = (miniapp: MiniApp) => {
    navigate(`/miniapp/${miniapp.id}`);
  };

  const handleArchiveMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'archived' });
    } catch (error) {
      console.error('Failed to archive miniapp:', error);
    }
  };

  const handleActivateMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'active' });
    } catch (error) {
      console.error('Failed to activate miniapp:', error);
    }
  };

  const handleDeleteMiniapp = (miniapp: MiniApp) => {
    setConfirmDelete(miniapp.id);
  };

  const handleCancelDelete = () => setConfirmDelete(null);

  const handleConfirmDelete = async () => {
    if (confirmDelete == null) return;
    try {
      await deleteMiniappService(confirmDelete);
    } catch (error) {
      console.error('Failed to delete miniapp:', error);
    } finally {
      setConfirmDelete(null);
    }
  };

  const handleShowArchived = () => {
    setShowArchivedView(true);
  };

  return (
    <div style={{ position: 'fixed', inset: 0, zIndex: 50 }}>
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundColor: '#ffffff',
          boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
          overflow: 'hidden',
          zIndex: 10,
        }}
      >
        {/* Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {showArchivedView ? getMessage('archived') : getMessage('miniapp')}
          </h2>
          <Tooltip title={getMessage('tooltipClose')} placement="bottom">
            <button
              onClick={() =>
                showArchivedView ? setShowArchivedView(false) : navigate('/conversation')
              }
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>

        {/* Content */}
        <div
          style={{
            flex: 1,
            height: 'calc(100vh - 44px)',
          }}
        >
          {/* Pin top bar */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '12px 16px 0px 16px',
              backgroundColor: '#ffffff',
            }}
          >
            {/* Filter dropdown */}
            <Dropdown
              menu={{
                items: filterItems,
                onClick: ({ key }) => setFilter(key as FilterType),
              }}
              trigger={['click']}
            >
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '8px 12px',
                  borderRadius: '6px',
                  border: '0px solid #d1d5db',
                  backgroundColor: '#ffffff',
                  color: '#374151',
                  fontSize: '14px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                }}
              >
                {filterLabel}
                <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                  <path
                    d="M3 4.5L6 7.5L9 4.5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    fill="none"
                  />
                </svg>
              </button>
            </Dropdown>

            {/* Archived button - only show in non-archived view */}
            <div style={{ display: 'flex', gap: '12px' }}>
              <Tooltip title={getMessage('newProject')}>
                <button
                  onClick={handleNewProject}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    borderRadius: '6px',
                    border: 'none',
                    backgroundColor: 'transparent',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <img src={newminiappIcon} alt="New Project" style={{ width: 18, height: 18 }} />
                </button>
              </Tooltip>
              <Tooltip title={getMessage('archived')}>
                <button
                  onClick={handleShowArchived}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    borderRadius: '6px',
                    border: 'none',
                    backgroundColor: 'transparent',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <img src={archiveIcon} alt="Archive" style={{ width: 18, height: 18 }} />
                </button>
              </Tooltip>
            </div>
          </div>
          {showArchivedView ? (
            // Archived view
            filteredMiniapps && filteredMiniapps.length > 0 ? (
              <MiniappApplicationsList
                miniapps={filteredMiniapps}
                onSelectMiniapp={handleSelectMiniapp}
                onNewProject={handleNewProject}
                onArchiveMiniapp={handleArchiveMiniapp}
                onDeleteMiniapp={handleDeleteMiniapp}
                onActivateMiniapp={handleActivateMiniapp}
                isArchivedView={true}
              />
            ) : (
              <MiniappEmptyState fromArchived={true} onNewProject={handleNewProject} />
            )
          ) : // Main view
          filteredMiniapps && filteredMiniapps.length > 0 ? (
            <MiniappApplicationsList
              miniapps={filteredMiniapps}
              onSelectMiniapp={handleSelectMiniapp}
              onNewProject={handleNewProject}
              onArchiveMiniapp={handleArchiveMiniapp}
              onDeleteMiniapp={handleDeleteMiniapp}
              onActivateMiniapp={handleActivateMiniapp}
              onShowArchived={handleShowArchived}
              isArchivedView={false}
            />
          ) : (
            <MiniappEmptyState fromArchived={false} onNewProject={handleNewProject} />
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <Modal
        open={confirmDelete != null}
        onCancel={handleCancelDelete}
        footer={
          <div style={{ display: 'flex', justifyContent: 'center', gap: 16, marginTop: 8 }}>
            <button
              onClick={handleCancelDelete}
              style={{
                fontWeight: 500,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: '1px solid #D1D5DB',
                color: '#111827',
                background: '#fff',
                cursor: 'pointer',
                transition: 'background 0.2s, border 0.2s',
              }}
            >
              {getMessage('cancel')}
            </button>
            <button
              onClick={handleConfirmDelete}
              style={{
                fontWeight: 600,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: 'none',
                background: '#DC2626',
                color: '#fff',
                cursor: 'pointer',
                boxShadow: '0 2px 8px 0 rgba(220,38,38,0.08)',
                transition: 'background 0.2s',
              }}
            >
              {getMessage('delete')}
            </button>
          </div>
        }
        centered
        closable={false}
        width={300}
        styles={{
          mask: { background: 'rgba(0,0,0,0.18)' },
          content: { borderRadius: 24 },
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <h3 style={{ fontSize: 22, fontWeight: 700, marginBottom: 18 }}>
            {getMessage('deleteMiniappTitle')}
          </h3>
          <div style={{ fontSize: 17, color: '#374151', marginBottom: 28 }}>
            {getMessage('deleteMiniappContent')}
          </div>
        </div>
      </Modal>

      {/* Create Project Modal */}
      <Modal
        open={showCreateModal}
        onCancel={handleCancelCreate}
        footer={null}
        centered
        width={300}
        closable={false}
      >
        <div style={{ textAlign: 'center' }}>
          <img
            src={newMiniappImg}
            alt="Create Project"
            style={{
              width: '80px',
              height: '80px',
              objectFit: 'contain',
            }}
          />

          {/* Title */}
          <h2
            style={{
              fontSize: '16px',
              fontWeight: 600,
              color: '#111827',
              marginBottom: '24px',
              margin: '0 0 24px 0',
            }}
          >
            {getMessage('createProjectTitle')}
          </h2>

          {/* Form */}
          <div style={{ textAlign: 'left', marginBottom: '24px' }}>
            <label
              style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: 500,
                color: '#374151',
                marginBottom: '6px',
              }}
            >
              {getMessage('projectNameLabel')}
            </label>
            <Input
              placeholder={getMessage('pleaseEnterPlaceholder')}
              value={projectName}
              onChange={e => setProjectName(e.target.value)}
              onPressEnter={handleCreateProject}
              style={{
                height: '40px',
                fontSize: '14px',
                borderRadius: '6px',
              }}
              autoFocus
            />
          </div>

          {/* Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <button
              onClick={handleCreateProject}
              disabled={!projectName.trim()}
              style={{
                width: '100%',
                height: '40px',
                borderRadius: '6px',
                border: 'none',
                backgroundColor: projectName.trim() ? '#000' : '#d1d5db',
                color: '#ffffff',
                fontSize: '14px',
                fontWeight: 500,
                cursor: projectName.trim() ? 'pointer' : 'not-allowed',
                transition: 'background-color 0.2s',
              }}
            >
              {getMessage('create')}
            </button>
            <button
              onClick={handleCancelCreate}
              style={{
                width: '100%',
                height: '40px',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '14px',
                fontWeight: 500,
                cursor: 'pointer',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
              }}
            >
              {getMessage('cancel')}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
